<style id="glyphs-style" type="text/css">
	.glyph{
		fill:currentColor;
		display:inline-block;
		margin-left:auto;
		margin-right:auto;
		position:relative;
		text-align:center;
		vertical-align:middle;
		width:70%;
		height:70%;
	}

	.glyph.sm{width:30%;height:30%;}
	.glyph.md{width:50%;height:50%;}
	.glyph.lg{height:100%;width:100%;}
	.glyph-svg{width:100%;height:100%;}
	.glyph-svg .fill{fill:inherit;}
	.glyph-svg .line{stroke:currentColor;stroke-width:inherit;}
	.glyph.spin{animation: spin 1s linear infinite;}

	@-webkit-keyframes spin {
		from { transform:rotate(0deg); }
		to { transform:rotate(360deg); }
	}
	@-moz-keyframes spin {
		from { transform:rotate(0deg); }
		to { transform:rotate(360deg); }
	}
	@keyframes spin {
		from { transform:rotate(0deg); }
		to { transform:rotate(360deg); }
	}
</style>
