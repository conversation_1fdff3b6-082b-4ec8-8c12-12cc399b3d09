<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Lumino - Icons</title>

<link href="css/bootstrap.min.css" rel="stylesheet">
<link href="css/datepicker3.css" rel="stylesheet">
<link href="css/styles.css" rel="stylesheet">

<!--Icons-->
<script src="js/lumino.glyphs.js"></script>

<!--[if lt IE 9]>
<script src="js/html5shiv.js"></script>
<script src="js/respond.min.js"></script>
<![endif]-->

</head>

<body>
	<nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
		<div class="container-fluid">
			<div class="navbar-header">
				<button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#sidebar-collapse">
					<span class="sr-only">Toggle navigation</span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>
				<a class="navbar-brand" href="#"><span>Lumino</span>Admin</a>
				<ul class="user-menu">
					<li class="dropdown pull-right">
						<a href="#" class="dropdown-toggle" data-toggle="dropdown"><svg class="glyph stroked male-user"><use xlink:href="#stroked-male-user"></use></svg> User <span class="caret"></span></a>
						<ul class="dropdown-menu" role="menu">
							<li><a href="#"><svg class="glyph stroked male-user"><use xlink:href="#stroked-male-user"></use></svg> Profile</a></li>
							<li><a href="#"><svg class="glyph stroked gear"><use xlink:href="#stroked-gear"></use></svg> Settings</a></li>
							<li><a href="#"><svg class="glyph stroked cancel"><use xlink:href="#stroked-cancel"></use></svg> Logout</a></li>
						</ul>
					</li>
				</ul>
			</div>
							
		</div><!-- /.container-fluid -->
	</nav>
		
	<div id="sidebar-collapse" class="col-sm-3 col-lg-2 sidebar">
		<form role="search">
			<div class="form-group">
				<input type="text" class="form-control" placeholder="Search">
			</div>
		</form>
		<ul class="nav menu">
			<li><a href="index.html"><svg class="glyph stroked dashboard-dial"><use xlink:href="#stroked-dashboard-dial"></use></svg> Dashboard</a></li>
			<li><a href="widgets.html"><svg class="glyph stroked calendar"><use xlink:href="#stroked-calendar"></use></svg> Widgets</a></li>
			<li><a href="charts.html"><svg class="glyph stroked line-graph"><use xlink:href="#stroked-line-graph"></use></svg> Charts</a></li>
			<li><a href="tables.html"><svg class="glyph stroked table"><use xlink:href="#stroked-table"></use></svg> Tables</a></li>
			<li><a href="forms.html"><svg class="glyph stroked pencil"><use xlink:href="#stroked-pencil"></use></svg> Forms</a></li>
			<li><a href="panels.html"><svg class="glyph stroked app-window"><use xlink:href="#stroked-app-window"></use></svg> Alerts &amp; Panels</a></li>
			<li class="active"><a href="icons.html"><svg class="glyph stroked star"><use xlink:href="#stroked-star"></use></svg> Icons</a></li>
			<li class="parent ">
				<a href="#">
					<span data-toggle="collapse" href="#sub-item-1"><svg class="glyph stroked chevron-down"><use xlink:href="#stroked-chevron-down"></use></svg></span> Dropdown 
				</a>
				<ul class="children collapse" id="sub-item-1">
					<li>
						<a class="" href="#">
							<svg class="glyph stroked chevron-right"><use xlink:href="#stroked-chevron-right"></use></svg> Sub Item 1
						</a>
					</li>
					<li>
						<a class="" href="#">
							<svg class="glyph stroked chevron-right"><use xlink:href="#stroked-chevron-right"></use></svg> Sub Item 2
						</a>
					</li>
					<li>
						<a class="" href="#">
							<svg class="glyph stroked chevron-right"><use xlink:href="#stroked-chevron-right"></use></svg> Sub Item 3
						</a>
					</li>
				</ul>
			</li>
			<li role="presentation" class="divider"></li>
			<li><a href="login.html"><svg class="glyph stroked male-user"><use xlink:href="#stroked-male-user"></use></svg> Login Page</a></li>
		</ul>
		<div class="attribution">Template by <a href="http://www.medialoot.com/item/lumino-admin-bootstrap-template/">Medialoot</a><br/><a href="http://www.glyphs.co" style="color: #333;">Icons by Glyphs</a></div>
	</div><!--/.sidebar-->
		
	<div class="col-sm-9 col-sm-offset-3 col-lg-10 col-lg-offset-2 main">			
		<div class="row">
			<ol class="breadcrumb">
				<li><a href="#"><svg class="glyph stroked home"><use xlink:href="#stroked-home"></use></svg></a></li>
				<li class="active">Icons</li>
			</ol>
		</div><!--/.row-->
		
		<div class="row">
			<div class="col-lg-12">
				<h1 class="page-header">All Icons</h1>
			</div>
		</div><!--/.row-->
				
		
		<div class="row">
			<div class="col-lg-12">
				<div class="panel panel-default">
					<div class="panel-heading">
						<strong>Stroked Icon Set</strong> <em>(full set available via <a href="https://glyphs.co/icons/stroked">Glyphs.co</a>)</em>
					</div>
					<div class="panel-body">
						<div class="icon-grid">
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked app window with content"><use xlink:href="#stroked-app-window-with-content"/></svg>
								<pre>&lt;svg class="glyph stroked app window with content">&lt;use xlink:href="#stroked-app-window-with-content"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked app-window"><use xlink:href="#stroked-app-window"></use></svg>
								<pre>&lt;svg class="glyph stroked app-window">&lt;use xlink:href="#stroked-app-window">&lt;/use>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked bag"><use xlink:href="#stroked-bag"></use></svg>
								<pre>&lt;svg class="glyph stroked bag">&lt;use xlink:href="#stroked-bag">&lt;/use>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked basket "><use xlink:href="#stroked-basket"/></svg>
								<pre>&lt;svg class="glyph stroked basket ">&lt;use xlink:href="#stroked-basket"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked calendar blank"><use xlink:href="#stroked-calendar-blank"/></svg>
								<pre>&lt;svg class="glyph stroked calendar blank">&lt;use xlink:href="#stroked-calendar-blank"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked calendar"><use xlink:href="#stroked-calendar"/></svg>
								<pre>&lt;svg class="glyph stroked calendar">&lt;use xlink:href="#stroked-calendar"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked camera "><use xlink:href="#stroked-camera"/></svg>
								<pre>&lt;svg class="glyph stroked camera ">&lt;use xlink:href="#stroked-camera"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked camcorder"><use xlink:href="#stroked-camcorder"/></svg>
								<pre>&lt;svg class="glyph stroked camcorder">&lt;use xlink:href="#stroked-camcorder"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked cancel"><use xlink:href="#stroked-cancel"/></svg>
								<pre>&lt;svg class="glyph stroked cancel">&lt;use xlink:href="#stroked-cancel"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked checkmark"><use xlink:href="#stroked-checkmark"/></svg>
								<pre>&lt;svg class="glyph stroked checkmark">&lt;use xlink:href="#stroked-checkmark"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked clipboard with paper"><use xlink:href="#stroked-clipboard-with-paper"/></svg>
								<pre>&lt;svg class="glyph stroked clipboard with paper">&lt;use xlink:href="#stroked-clipboard-with-paper"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked clock"><use xlink:href="#stroked-clock"/></svg>
								<pre>&lt;svg class="glyph stroked clock">&lt;use xlink:href="#stroked-clock"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked dashboard dial"><use xlink:href="#stroked-dashboard-dial"/></svg>
								<pre>&lt;svg class="glyph stroked dashboard dial">&lt;use xlink:href="#stroked-dashboard-dial"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked desktop"><use xlink:href="#stroked-desktop"/></svg>
								<pre>&lt;svg class="glyph stroked desktop">&lt;use xlink:href="#stroked-desktop"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked blank document"><use xlink:href="#stroked-blank-document"/></svg>
								<pre>&lt;svg class="glyph stroked blank document">&lt;use xlink:href="#stroked-blank-document"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked download"><use xlink:href="#stroked-download"/></svg>
								<pre>&lt;svg class="glyph stroked download">&lt;use xlink:href="#stroked-download"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked eye"><use xlink:href="#stroked-eye"/></svg>
								<pre>&lt;svg class="glyph stroked eye">&lt;use xlink:href="#stroked-eye"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked open folder"><use xlink:href="#stroked-open-folder"/></svg>
								<pre>&lt;svg class="glyph stroked open folder">&lt;use xlink:href="#stroked-open-folder"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked folder"><use xlink:href="#stroked-folder"/></svg>
								<pre>&lt;svg class="glyph stroked folder">&lt;use xlink:href="#stroked-folder"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked gear"><use xlink:href="#stroked-gear"/></svg>
								<pre>&lt;svg class="glyph stroked gear">&lt;use xlink:href="#stroked-gear"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked home"><use xlink:href="#stroked-home"/></svg>
								<pre>&lt;svg class="glyph stroked home">&lt;use xlink:href="#stroked-home"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked heart"><use xlink:href="#stroked-heart"/></svg>
								<pre>&lt;svg class="glyph stroked heart">&lt;use xlink:href="#stroked-heart"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked key "><use xlink:href="#stroked-key"/></svg>
								<pre>&lt;svg class="glyph stroked key ">&lt;use xlink:href="#stroked-key"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked chain"><use xlink:href="#stroked-chain"/></svg>
								<pre>&lt;svg class="glyph stroked chain">&lt;use xlink:href="#stroked-chain"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked location pin"><use xlink:href="#stroked-location-pin"/></svg>
								<pre>&lt;svg class="glyph stroked location pin">&lt;use xlink:href="#stroked-location-pin"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked lock"><use xlink:href="#stroked-lock"/></svg>
								<pre>&lt;svg class="glyph stroked lock">&lt;use xlink:href="#stroked-lock"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked map"><use xlink:href="#stroked-map"/></svg>
								<pre>&lt;svg class="glyph stroked map">&lt;use xlink:href="#stroked-map"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked empty message"><use xlink:href="#stroked-empty-message"/></svg>
								<pre>&lt;svg class="glyph stroked empty message">&lt;use xlink:href="#stroked-empty-message"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked two messages"><use xlink:href="#stroked-two-messages"/></svg>
								<pre>&lt;svg class="glyph stroked two messages">&lt;use xlink:href="#stroked-two-messages"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked mobile device"><use xlink:href="#stroked-mobile-device"/></svg>
								<pre>&lt;svg class="glyph stroked mobile device">&lt;use xlink:href="#stroked-mobile-device"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked music"><use xlink:href="#stroked-music"/></svg>
								<pre>&lt;svg class="glyph stroked music">&lt;use xlink:href="#stroked-music"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked notepad "><use xlink:href="#stroked-notepad"/></svg>
								<pre>&lt;svg class="glyph stroked notepad ">&lt;use xlink:href="#stroked-notepad"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked paperclip"><use xlink:href="#stroked-paperclip"/></svg>
								<pre>&lt;svg class="glyph stroked paperclip">&lt;use xlink:href="#stroked-paperclip"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked landscape"><use xlink:href="#stroked-landscape"/></svg>
								<pre>&lt;svg class="glyph stroked landscape">&lt;use xlink:href="#stroked-landscape"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked plus sign"><use xlink:href="#stroked-plus-sign"/></svg>
								<pre>&lt;svg class="glyph stroked plus sign">&lt;use xlink:href="#stroked-plus-sign"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked printer"><use xlink:href="#stroked-printer"/></svg>
								<pre>&lt;svg class="glyph stroked printer">&lt;use xlink:href="#stroked-printer"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked sound on"><use xlink:href="#stroked-sound-on"/></svg>
								<pre>&lt;svg class="glyph stroked sound on">&lt;use xlink:href="#stroked-sound-on"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked star"><use xlink:href="#stroked-star"/></svg>
								<pre>&lt;svg class="glyph stroked star">&lt;use xlink:href="#stroked-star"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked table"><use xlink:href="#stroked-table"/></svg>
								<pre>&lt;svg class="glyph stroked table">&lt;use xlink:href="#stroked-table"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked tablet"><use xlink:href="#stroked-tablet-1"/></svg>
								<pre>&lt;svg class="glyph stroked tablet">&lt;use xlink:href="#stroked-tablet-1"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked tag"><use xlink:href="#stroked-tag"/></svg>
								<pre>&lt;svg class="glyph stroked tag">&lt;use xlink:href="#stroked-tag"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked trash"><use xlink:href="#stroked-trash"/></svg>
								<pre>&lt;svg class="glyph stroked trash">&lt;use xlink:href="#stroked-trash"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked monitor"><use xlink:href="#stroked-monitor"/></svg>
								<pre>&lt;svg class="glyph stroked monitor">&lt;use xlink:href="#stroked-monitor"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked upload"><use xlink:href="#stroked-upload"/></svg>
								<pre>&lt;svg class="glyph stroked upload">&lt;use xlink:href="#stroked-upload"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked male user "><use xlink:href="#stroked-male-user"/></svg>
								<pre>&lt;svg class="glyph stroked male user ">&lt;use xlink:href="#stroked-male-user"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked film"><use xlink:href="#stroked-film"/></svg>
								<pre>&lt;svg class="glyph stroked film">&lt;use xlink:href="#stroked-film"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked female user"><use xlink:href="#stroked-female-user"/></svg>
								<pre>&lt;svg class="glyph stroked female user">&lt;use xlink:href="#stroked-female-user"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked video"><use xlink:href="#stroked-video"/></svg>
								<pre>&lt;svg class="glyph stroked video">&lt;use xlink:href="#stroked-video"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked arrow down"><use xlink:href="#stroked-arrow-down"/></svg>
								<pre>&lt;svg class="glyph stroked arrow down">&lt;use xlink:href="#stroked-arrow-down"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked arrow left"><use xlink:href="#stroked-arrow-left"/></svg>
								<pre>&lt;svg class="glyph stroked arrow left">&lt;use xlink:href="#stroked-arrow-left"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked arrow right"><use xlink:href="#stroked-arrow-right"/></svg>
								<pre>&lt;svg class="glyph stroked arrow right">&lt;use xlink:href="#stroked-arrow-right"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked arrow up"><use xlink:href="#stroked-arrow-up"/></svg>
								<pre>&lt;svg class="glyph stroked arrow up">&lt;use xlink:href="#stroked-arrow-up"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked chevron down"><use xlink:href="#stroked-chevron-down"/></svg>
								<pre>&lt;svg class="glyph stroked chevron down">&lt;use xlink:href="#stroked-chevron-down"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked chevron left"><use xlink:href="#stroked-chevron-left"/></svg>
								<pre>&lt;svg class="glyph stroked chevron left">&lt;use xlink:href="#stroked-chevron-left"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked chevron right"><use xlink:href="#stroked-chevron-right"/></svg>
								<pre>&lt;svg class="glyph stroked chevron right">&lt;use xlink:href="#stroked-chevron-right"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked chevron up"><use xlink:href="#stroked-chevron-up"/></svg>
								<pre>&lt;svg class="glyph stroked chevron up">&lt;use xlink:href="#stroked-chevron-up"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked desktop computer and mobile"><use xlink:href="#stroked-desktop-computer-and-mobile"/></svg>
								<pre>&lt;svg class="glyph stroked desktop computer and mobile">&lt;use xlink:href="#stroked-desktop-computer-and-mobile"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked flag"><use xlink:href="#stroked-flag"/></svg>
								<pre>&lt;svg class="glyph stroked flag">&lt;use xlink:href="#stroked-flag"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked external hard drive"><use xlink:href="#stroked-external-hard-drive"/></svg>
								<pre>&lt;svg class="glyph stroked external hard drive">&lt;use xlink:href="#stroked-external-hard-drive"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked internal hard drive"><use xlink:href="#stroked-internal-hard-drive"/></svg>
								<pre>&lt;svg class="glyph stroked internal hard drive">&lt;use xlink:href="#stroked-internal-hard-drive"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked hourglass"><use xlink:href="#stroked-hourglass"/></svg>
								<pre>&lt;svg class="glyph stroked hourglass">&lt;use xlink:href="#stroked-hourglass"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked laptop computer and mobile"><use xlink:href="#stroked-laptop-computer-and-mobile"/></svg>
								<pre>&lt;svg class="glyph stroked laptop computer and mobile">&lt;use xlink:href="#stroked-laptop-computer-and-mobile"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked open letter"><use xlink:href="#stroked-open-letter"/></svg>
								<pre>&lt;svg class="glyph stroked open letter">&lt;use xlink:href="#stroked-open-letter"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked email"><use xlink:href="#stroked-email"/></svg>
								<pre>&lt;svg class="glyph stroked email">&lt;use xlink:href="#stroked-email"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked brush"><use xlink:href="#stroked-brush"/></svg>
								<pre>&lt;svg class="glyph stroked brush">&lt;use xlink:href="#stroked-brush"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked pencil"><use xlink:href="#stroked-pencil"/></svg>
								<pre>&lt;svg class="glyph stroked pencil">&lt;use xlink:href="#stroked-pencil"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked toiler paper"><use xlink:href="#stroked-toiler-paper"/></svg>
								<pre>&lt;svg class="glyph stroked toiler paper">&lt;use xlink:href="#stroked-toiler-paper"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked usb flash drive"><use xlink:href="#stroked-usb-flash-drive"/></svg>
								<pre>&lt;svg class="glyph stroked usb flash drive">&lt;use xlink:href="#stroked-usb-flash-drive"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked pen tip"><use xlink:href="#stroked-pen-tip"/></svg>
								<pre>&lt;svg class="glyph stroked pen tip">&lt;use xlink:href="#stroked-pen-tip"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked wireless router"><use xlink:href="#stroked-wireless-router"/></svg>
								<pre>&lt;svg class="glyph stroked wireless router">&lt;use xlink:href="#stroked-wireless-router"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked round coffee mug"><use xlink:href="#stroked-round-coffee-mug"/></svg>
								<pre>&lt;svg class="glyph stroked round coffee mug">&lt;use xlink:href="#stroked-round-coffee-mug"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked paper coffee cup"><use xlink:href="#stroked-paper-coffee-cup"/></svg>
								<pre>&lt;svg class="glyph stroked paper coffee cup">&lt;use xlink:href="#stroked-paper-coffee-cup"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked bacon burger"><use xlink:href="#stroked-bacon-burger"/></svg>
								<pre>&lt;svg class="glyph stroked bacon burger">&lt;use xlink:href="#stroked-bacon-burger"/>&lt;/svg></pre>
							</div>
							<div class="col-lg-3 col-md-4 col-sm-6">
								<svg class="glyph stroked line-graph"><use xlink:href="#stroked-line-graph"></use></svg>
								<pre>&lt;svg class="glyph stroked line-graph">&lt;use xlink:href="#stroked-line-graph">&lt;/use>&lt;/svg></pre>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div><!--/.row-->		
		
		
	</div><!--/.main-->

	<script src="js/jquery-1.11.1.min.js"></script>
	<script src="js/bootstrap.min.js"></script>
	<script src="js/chart.min.js"></script>
	<script src="js/chart-data.js"></script>
	<script src="js/easypiechart.js"></script>
	<script src="js/easypiechart-data.js"></script>
	<script src="js/bootstrap-datepicker.js"></script>
	<script>
		!function ($) {
			$(document).on("click","ul.nav li.parent > a > span.icon", function(){		  
				$(this).find('em:first').toggleClass("glyphicon-minus");	  
			}); 
			$(".sidebar span.icon").find('em:first').addClass("glyphicon-plus");
		}(window.jQuery);

		$(window).on('resize', function () {
		  if ($(window).width() > 768) $('#sidebar-collapse').collapse('show')
		})
		$(window).on('resize', function () {
		  if ($(window).width() <= 767) $('#sidebar-collapse').collapse('hide')
		})
	</script>	
</body>

</html>
