a:hover{
    text-decoration: none;
}
.clearpadding{
    padding-left: 0px;
    padding-right: 0px;
}
.clearpaddingl{
    padding-left: 0px;
}
.clearpaddingr{
    padding-right: 0px;
}
.re-padding{
    padding-right: 5px;
    padding-left: 5px;
}
.row{
    /*box-shadow: 0 0 10px #bce8f1;*/
}
.re-navbar{
    margin-bottom: 10px;
    border: none;
    border-bottom: 3px solid rgb(238, 77, 45);
}
.re-container-fluid{
    padding-right: 0px;
    padding-left: 0px;
}
.btn-default:hover{
    background-color: #ffffff;
    color: rgb(238, 77, 45);
}

#re-dropdown-menu{
    padding-top: 0px;
    padding-bottom: 0px;
}
.navbar-info .navbar-toggle {
    border-color: #ddd;
}
.navbar-info .navbar-toggle .icon-bar {
    background-color: #888;
}
.navbar-info .navbar-nav > .active > a,
.navbar-info .navbar-nav > .active > a:hover,
.navbar-info .navbar-nav > .active > a:focus {
    color: #ffffff;
    background-color: rgb(238, 77, 45);
}
.navbar-nav > li > a{
    font-weight: bold;
}
.navbar-info .navbar-nav > li > a:hover,
.navbar-info .navbar-nav > li > a:focus {
    color: #ffffff;
    background-color: rgb(238, 77, 45);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.panel{
    margin-bottom: 0px;
}
.product_item{
    border: 1px solid #ffffff;
    border-radius: 10px;
    text-align: center;
    padding-top: 8px;
    padding-bottom: 8px;
    margin-bottom: 5px;
    background-color: #ffffff;
    /*margin: 16px;*/
}
.product_inve{
    border: 1px solid #DDDDDD;
    border-radius: 10px;
    text-align: center;
    padding: 8px 0px;
    margin: 12px;
    width: 22%;
}
.product_item:hover{
    border: 1px solid rgb(238, 77, 45);
}

.product_item .label_pro{
    color: #ffffff;
    background: red;
    font-size: 10px;
    font-weight: 700;
    letter-spacing: 2px;
    text-transform: uppercase;
    display: inline-block;
    padding: 5px 15px 2px;
    position: absolute;
    left: 5px;
    top: 10px;
}
/*margin: 16px;*/

#seach_info[type=text] {
    width: 90px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    font-size: 15px;
    background-color: white;
    background-position: 10px 10px; 
    background-repeat: no-repeat;
    padding: 6px 5px 5px 5px;
    -webkit-transition: width 0.4s ease-in-out;
    transition: width 0.4s ease-in-out;
}

#seach_info[type=text]:focus {
    width: 85%;
    outline-color:  rgb(238, 77, 45) !important;
    
}
.btn-search_info {
  float: left;
  padding: 5px 8px 7px 8px;
  margin-right: 0px;
  background: rgb(238, 77, 45);
  color: white;
  font-size: 16px;
  border: none;
  cursor: pointer;
}


.product_title{
    text-transform: uppercase;
    font-size: 14px;
    font-weight: bold;
}
a.product_title:hover{
    color: rgb(238, 77, 45);
}
.dropdown-btn {
    position: relative;
    display: block;
    padding: 10px 15px;
    margin-bottom: -1px;
    background-color: #f5f5f5;
    border: 1px solid #f5f5f5;
}

.fa-caret-down {
    margin-top: 2%;
    float: right;
    color: rgb(238, 77, 45);
}
.product_name {
    height: 55px;
    text-transform: uppercase;
    font-size: 14px;
    color: #111111;
    font-weight: bold;
}
.price{
    color: red;
    font-weight: bold;
}
.product-image{
    margin-top:8px;
    margin-bottom:8px;
}
.product-image img{
    width: 95%;
    height: 275px;
    -webkit-transform:scale(0.9); /*Webkit: Thu nhỏ kích thước ảnh còn 0.8 so với ảnh gốc*/
    -moz-transform:scale(0.9); /*Thu nhỏ đối với Mozilla*/
    -o-transform:scale(0.9); /*Thu nhỏ đối với Opera*/
    transition: all .4s ease;
    -webkit-transition: all .4s ease;
    -moz-transition: all .4s ease;
    -o-transition: all .4s ease;
}
.product-image img:hover{
    transform: scale(1);
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    -ms-transform: scale(1);
    border-radius: 10px;
}
.cart-sumsub{
    padding: 4px 8px;
    border: 1px solid #333;
}
#bank{
    border-left: 1px solid #bce8f1;
    border-top: 1px solid #bce8f1;
    border-right: 1px solid #bce8f1;
    padding-top: 20px;
    padding-bottom: 25px;
}

.fi-left {
    width: 40%;
    height: 100%;
    font-style: normal;
}
.pull-left {
    float: left!important;
}
.fi-right {
    width: 60%;
    height: 100%;
    font-style: normal;
}
.pull-right {
    float: right!important;
}
#footer{
    background: #111111;
    border-radius: 0 0 3px 3px;
    border-top: 3px solid #111111;
    height: auto;
    color : #b7b7b7;
    font-family: "Nunito Sans", sans-serif;
    padding-bottom: 30px;
    padding-top: 5px;
    padding: 10px 15px;
    margin-bottom: 7px;
}
address {
    margin-bottom: 10px;
}

.breadcrumb{
    margin-bottom: 8px;
}
.list-group{
    margin-bottom: 0px;
}
.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus{
    color: #fff;
    background-color: #333;
    border-color: #none;
}
.dropdown-btn.active {
    background-color: rgb(238, 77, 45) !important;
    color: white;
}
.dropdown-btn.active a{
    color: white;
}
.dropdown-btn.active .fa-caret-down{
    color: #ffffff;
}
#thumblist li{
    list-style-type: none;
    text-align: center;
}
#thumblist li a img{
    width: 50px;
}









.footer {
	background: #111111;
	padding-top: 70px;
        position: absolute;
        width: 100%;
}

.footer__about {
	margin-bottom: 30px;
}

.footer__about .footer__logo {
	margin-bottom: 30px;
}

.footer__about .footer__logo a {
	display: inline-block;
}

.footer__about p {
	color: #b7b7b7;
	margin-bottom: 30px;
}

.footer__widget {
	margin-bottom: 30px;
}

.footer__widget h6 {
	color: #ffffff;
	font-size: 15px;
	font-weight: 700;
	text-transform: uppercase;
	letter-spacing: 2px;
	margin-bottom: 20px;
}

.footer__widget ul li {
	line-height: 36px;
	list-style: none;
}

.footer__widget ul li a {
	color: #b7b7b7;
	font-size: 15px;
}

.footer__widget .footer__newslatter p {
	color: #b7b7b7;
}

.footer__widget .footer__newslatter form {
	position: relative;
}

.footer__widget .footer__newslatter form input {
	width: 100%;
	font-size: 15px;
	color: #3d3d3d;
	background: transparent;
	border: none;
	padding: 15px 0;
	border-bottom: 2px solid #ffffff;
}

.footer__widget .footer__newslatter form input::-webkit-input-placeholder {
	color: #3d3d3d;
}

.footer__widget .footer__newslatter form input::-moz-placeholder {
	color: #3d3d3d;
}

.footer__widget .footer__newslatter form input:-ms-input-placeholder {
	color: #3d3d3d;
}

.footer__widget .footer__newslatter form input::-ms-input-placeholder {
	color: #3d3d3d;
}

.footer__widget .footer__newslatter form input::placeholder {
	color: #3d3d3d;
}

.footer__widget .footer__newslatter form button {
	color: #b7b7b7;
	font-size: 16px;
	position: absolute;
	right: 5px;
	top: 0;
	height: 100%;
	background: transparent;
	border: none;
}

.footer__copyright__text {
	border-top: 1px solid rgba(255, 255, 255, 0.1);
	padding: 20px 0;
	margin-top: 40px;
}

.footer__copyright__text p {
	color: #b7b7b7;
	margin-bottom: 0;
}

.footer__copyright__text p i {
	color: #e53637;
}

.footer__copyright__text p a {
	color: #e53637;
}
.fa-email {
    color: #b7b7b7;
    font-size: 16px;
    display: inline-block;
}
.cus-color {
    color: #ffffff !important;
    background-color: rgb(240, 93, 64) !important;

}
@media (min-width: 768px) {
    .container > .navbar-header,
    .container-fluid > .navbar-header,
    .container > .navbar-collapse,
    .container-fluid > .navbar-collapse {
        display: none;
        margin-left: -15px;
    }
    .modal-sm {
        width: 400px;
    }
}
